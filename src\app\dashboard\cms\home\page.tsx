'use client';

import { useRouter } from 'next/navigation';
import PageContainer from '@/components/layout/page-container';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Eye, Edit } from 'lucide-react';
import {
  useGetOpportunitySection,
  useGetTalentSection,
  useGetDiscoverSection,
  useGetCompaniesSection,
  useGetPartnersSection,
  useGetCtaSection,
  useGetAppSection
} from '@/hooks/useQuery';

type SectionType =
  | 'opportunity'
  | 'talent'
  | 'discover'
  | 'companies'
  | 'partners'
  | 'cta'
  | 'app';

export default function HomepageSectionsPage() {
  const router = useRouter();

  // Fetch all sections
  const { data: opportunityData, isLoading: opportunityLoading } =
    useGetOpportunitySection();
  const { data: talentData, isLoading: talentLoading } = useGetTalentSection();
  const { data: discoverData, isLoading: discoverLoading } =
    useGetDiscoverSection();
  const { data: companiesData, isLoading: companiesLoading } =
    useGetCompaniesSection();
  const { data: partnersData, isLoading: partnersLoading } =
    useGetPartnersSection();
  const { data: ctaData, isLoading: ctaLoading } = useGetCtaSection();
  const { data: appData, isLoading: appLoading } = useGetAppSection();

  const sections = [
    {
      id: 'opportunity' as SectionType,
      title: 'Opportunity Section',
      description: 'Showcase opportunities with steps and images',
      data: opportunityData?.data?.section,
      isLoading: opportunityLoading,
      color: 'bg-blue-50 border-blue-200',
      badge: 'primary'
    },
    {
      id: 'talent' as SectionType,
      title: 'Talent Section',
      description: 'Highlight talent acquisition process',
      data: talentData?.data?.section,
      isLoading: talentLoading,
      color: 'bg-green-50 border-green-200',
      badge: 'secondary'
    },
    {
      id: 'discover' as SectionType,
      title: 'Discover Section',
      description: 'Help users discover opportunities',
      data: discoverData?.data?.section,
      isLoading: discoverLoading,
      color: 'bg-purple-50 border-purple-200',
      badge: 'outline'
    },
    {
      id: 'companies' as SectionType,
      title: 'Companies Section',
      description: 'Showcase partner companies',
      data: companiesData?.data?.section,
      isLoading: companiesLoading,
      color: 'bg-orange-50 border-orange-200',
      badge: 'secondary'
    },
    {
      id: 'partners' as SectionType,
      title: 'Partners Section',
      description: 'Display trusted partners',
      data: partnersData?.data?.section,
      isLoading: partnersLoading,
      color: 'bg-pink-50 border-pink-200',
      badge: 'outline'
    },
    {
      id: 'cta' as SectionType,
      title: 'Call to Action Section',
      description: 'Encourage user engagement',
      data: ctaData?.data?.section,
      isLoading: ctaLoading,
      color: 'bg-red-50 border-red-200',
      badge: 'destructive'
    },
    {
      id: 'app' as SectionType,
      title: 'Mobile App Section',
      description: 'Promote mobile app downloads',
      data: appData?.data?.section,
      isLoading: appLoading,
      color: 'bg-indigo-50 border-indigo-200',
      badge: 'primary'
    }
  ];

  const handleView = (sectionType: SectionType) => {
    router.push(`/dashboard/cms/home/<USER>/view`);
  };

  const handleEdit = (sectionType: SectionType) => {
    router.push(`/dashboard/cms/home/<USER>/edit`);
  };

  const renderSectionCard = (section: any) => {
    if (section.isLoading) {
      return (
        <Card key={section.id} className='h-full'>
          <CardHeader>
            <Skeleton className='h-6 w-3/4' />
            <Skeleton className='h-4 w-full' />
          </CardHeader>
          <CardContent>
            <div className='space-y-2'>
              <Skeleton className='h-4 w-full' />
              <Skeleton className='h-4 w-2/3' />
              <div className='flex gap-2 pt-4'>
                <Skeleton className='h-9 w-20' />
                <Skeleton className='h-9 w-20' />
              </div>
            </div>
          </CardContent>
        </Card>
      );
    }

    return (
      <Card
        key={section.id}
        className={`h-full transition-all hover:shadow-md ${section.color}`}
      >
        <CardHeader>
          <div className='flex items-center justify-between'>
            <CardTitle className='text-lg'>{section.title}</CardTitle>
            <Badge variant={section.badge as any}>{section.id}</Badge>
          </div>
          <p className='text-muted-foreground text-sm'>{section.description}</p>
        </CardHeader>
        <CardContent>
          <div className='space-y-3'>
            {section.data ? (
              <div className='space-y-2'>
                <div>
                  <span className='text-sm font-medium'>Heading:</span>
                  <p className='text-muted-foreground truncate text-sm'>
                    {section.data.heading}
                  </p>
                </div>
                <div>
                  <span className='text-sm font-medium'>Description:</span>
                  <p className='text-muted-foreground line-clamp-2 text-sm'>
                    {section.data.description}
                  </p>
                </div>
                {section.data.steps && (
                  <div>
                    <span className='text-sm font-medium'>Steps:</span>
                    <p className='text-muted-foreground text-sm'>
                      {section.data.steps.length} steps
                    </p>
                  </div>
                )}
                {section.data.partners && (
                  <div>
                    <span className='text-sm font-medium'>Partners:</span>
                    <p className='text-muted-foreground text-sm'>
                      {section.data.partners.length} partners
                    </p>
                  </div>
                )}
              </div>
            ) : (
              <p className='text-muted-foreground text-sm'>No data available</p>
            )}

            <div className='flex gap-2 pt-4'>
              <Button
                variant='outline'
                size='sm'
                onClick={() => handleView(section.id)}
                disabled={!section.data}
              >
                <Eye className='mr-1 h-4 w-4' />
                View
              </Button>
              <Button
                size='sm'
                onClick={() => handleEdit(section.id)}
                disabled={!section.data}
              >
                <Edit className='mr-1 h-4 w-4' />
                Edit
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <PageContainer>
      <div className='flex flex-1 flex-col space-y-6'>
        <div>
          <h1 className='text-3xl font-bold'>Homepage Sections</h1>
          <p className='text-muted-foreground'>
            Manage all sections of your homepage content. View and edit each
            section to customize your site.
          </p>
        </div>

        <div className='grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3'>
          {sections.map(renderSectionCard)}
        </div>
      </div>
    </PageContainer>
  );
}
