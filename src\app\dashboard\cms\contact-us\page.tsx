'use client';

import { useRouter } from 'next/navigation';
import PageContainer from '@/components/layout/page-container';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Eye, Edit } from 'lucide-react';
import { useGetContactUsPage } from '@/hooks/useQuery';

export default function ContactUsCMSPage() {
  const router = useRouter();

  // Fetch Contact Us page data
  const { data: contactUsData, isLoading: contactUsLoading } = useGetContactUsPage();

  const sections = [
    {
      id: 'main-section',
      title: 'Main Section',
      description: 'Contact us page heading, description and hero image',
      data: contactUsData?.data?.page,
      isLoading: contactUsLoading,
      color: 'bg-blue-50 border-blue-200',
      badge: 'primary'
    }
  ];

  const handleView = () => {
    router.push('/dashboard/cms/contact-us/view');
  };

  const handleEdit = () => {
    router.push('/dashboard/cms/contact-us/edit');
  };

  const renderSectionCard = (section: any) => {
    if (section.isLoading) {
      return (
        <Card key={section.id} className='h-full'>
          <CardHeader>
            <Skeleton className='h-6 w-3/4' />
            <Skeleton className='h-4 w-full' />
          </CardHeader>
          <CardContent>
            <div className='space-y-2'>
              <Skeleton className='h-4 w-full' />
              <Skeleton className='h-4 w-2/3' />
            </div>
          </CardContent>
        </Card>
      );
    }

    return (
      <Card
        key={section.id}
        className={`h-full transition-all hover:shadow-md ${section.color}`}
      >
        <CardHeader>
          <div className='flex items-center justify-between'>
            <CardTitle className='text-lg'>{section.title}</CardTitle>
            <Badge variant={section.badge as any}>{section.id}</Badge>
          </div>
          <p className='text-muted-foreground text-sm'>{section.description}</p>
        </CardHeader>
        <CardContent>
          <div className='space-y-3'>
            {section.data ? (
              <div className='space-y-2'>
                <div>
                  <span className='text-sm font-medium'>Heading:</span>
                  <p className='text-muted-foreground truncate text-sm'>
                    {section.data.heading}
                  </p>
                </div>
                <div>
                  <span className='text-sm font-medium'>Description:</span>
                  <p className='text-muted-foreground line-clamp-2 text-sm'>
                    {section.data.description}
                  </p>
                </div>
                {section.data.image && (
                  <div>
                    <span className='text-sm font-medium'>Image:</span>
                    <p className='text-muted-foreground text-sm'>Available</p>
                  </div>
                )}
              </div>
            ) : (
              <p className='text-muted-foreground text-sm'>No data available</p>
            )}
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <PageContainer>
      <div className='flex flex-1 flex-col space-y-6'>
        <div>
          <h1 className='text-3xl font-bold'>Contact Us Page</h1>
          <p className='text-muted-foreground'>
            Manage the Contact Us page content. View and edit the main section to
            customize your contact information.
          </p>
        </div>

        <div className='grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3'>
          {sections.map(renderSectionCard)}
        </div>

        <div className='flex gap-4 pt-6'>
          <Button
            variant='outline'
            size='lg'
            onClick={handleView}
            disabled={contactUsLoading || !contactUsData?.data?.page}
          >
            <Eye className='mr-2 h-4 w-4' />
            View Page
          </Button>
          <Button
            size='lg'
            onClick={handleEdit}
            disabled={contactUsLoading || !contactUsData?.data?.page}
          >
            <Edit className='mr-2 h-4 w-4' />
            Edit Page
          </Button>
        </div>
      </div>
    </PageContainer>
  );
}
