'use client';

import { useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';

interface QuillEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

export function QuillEditor({
  value,
  onChange,
  placeholder = 'Enter content...',
  className,
  disabled = false
}: QuillEditorProps) {
  const editorRef = useRef<HTMLDivElement>(null);
  const quillRef = useRef<any>(null);
  const isUpdatingRef = useRef(false);

  // ✅ Only init Quill after value is available
  useEffect(() => {
    if (
      typeof window === 'undefined' ||
      !editorRef.current ||
      quillRef.current ||
      !value // wait until value is loaded (e.g. from API)
    ) {
      return;
    }

    import('quill').then(({ default: Quill }) => {
      editorRef.current!.innerHTML = ''; // clear any previous DOM

      const q = new Quill(editorRef.current!, {
        theme: 'snow',
        placeholder,
        modules: {
          toolbar: [
            [{ header: [1, 2, 3, false] }],
            ['bold', 'italic', 'underline'],
            [{ list: 'ordered' }, { list: 'bullet' }],
            ['link', 'image'],
            ['clean']
          ]
        }
      });

      quillRef.current = q;

      // Set initial content
      isUpdatingRef.current = true;
      q.root.innerHTML = value || '';
      isUpdatingRef.current = false;

      q.on('text-change', () => {
        if (!isUpdatingRef.current) {
          const html = q.root.innerHTML;
          onChange(html);
        }
      });

      if (disabled) {
        q.disable();
      }
    });

    return () => {
      if (quillRef.current) {
        try {
          quillRef.current.off('text-change');
          quillRef.current = null;
        } catch (err) {
          console.warn('Error cleaning up Quill:', err);
        }
      }
    };
  }, [value]); // ⚠️ re-run when value changes from empty -> loaded

  // Sync disabled prop dynamically
  useEffect(() => {
    if (quillRef.current) {
      disabled ? quillRef.current.disable() : quillRef.current.enable();
    }
  }, [disabled]);

  return (
    <div className={cn('quill-editor', className)}>
      <div ref={editorRef} />
      <style jsx global>{`
        .quill-editor .ql-editor {
          min-height: 200px;
        }
        .quill-editor .ql-toolbar {
          border-top: 1px solid hsl(var(--border));
          border-left: 1px solid hsl(var(--border));
          border-right: 1px solid hsl(var(--border));
          border-bottom: none;
          border-radius: 0.375rem 0.375rem 0 0;
        }
        .quill-editor .ql-container {
          border-bottom: 1px solid hsl(var(--border));
          border-left: 1px solid hsl(var(--border));
          border-right: 1px solid hsl(var(--border));
          border-top: none;
          border-radius: 0 0 0.375rem 0.375rem;
        }
        .quill-editor .ql-editor.ql-blank::before {
          color: hsl(var(--muted-foreground));
        }
      `}</style>
    </div>
  );
}
