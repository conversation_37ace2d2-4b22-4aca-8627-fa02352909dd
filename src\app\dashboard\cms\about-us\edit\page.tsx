'use client';

import { useRouter } from 'next/navigation';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import PageContainer from '@/components/layout/page-container';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import { ArrowLeft, Save, Plus, Trash2, Upload } from 'lucide-react';
import { useGetAboutUsPage } from '@/hooks/useQuery';
import { useUpdateAboutUsSection } from '@/hooks/useMutation';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import Image from 'next/image';
import { DEFAULT_IMAGE } from '@/constants/app.const';
import { ImageUploadField } from '@/components/cms/image-upload-field';

// Validation schema
const aboutUsSchema = z.object({
  ourMission: z.object({
    heading: z.string().min(1, 'Heading is required'),
    description: z.string().min(1, 'Description is required')
  }),
  aboutUs: z.object({
    heading: z.string().min(1, 'Heading is required'),
    description: z.string().min(1, 'Description is required'),
    image: z.string().min(1, 'Image is required')
  }),
  ourVision: z.object({
    heading: z.string().min(1, 'Heading is required'),
    description: z.string().min(1, 'Description is required')
  }),
  ourValues: z.object({
    heading: z.string().min(1, 'Heading is required'),
    description: z.string().min(1, 'Description is required'),
    values: z.array(
      z.object({
        name: z.string().min(1, 'Name is required'),
        description: z.string().min(1, 'Description is required'),
        iconImage: z.string().min(1, 'Icon image is required')
      })
    )
  })
});

type AboutUsFormData = z.infer<typeof aboutUsSchema>;

export default function AboutUsEditPage() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch About Us page data
  const { data: aboutUsData, isLoading: aboutUsLoading } = useGetAboutUsPage();

  // Update mutation
  const updateMutation = useUpdateAboutUsSection({
    onSuccess: () => {
      setIsSubmitting(false);
      router.push('/dashboard/cms/about-us');
    },
    onError: () => {
      setIsSubmitting(false);
    }
  });

  // Form setup
  const {
    register,
    control,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors }
  } = useForm<AboutUsFormData>({
    resolver: zodResolver(aboutUsSchema),
    defaultValues: {
      ourMission: { heading: '', description: '' },
      aboutUs: { heading: '', description: '', image: '' },
      ourVision: { heading: '', description: '' },
      ourValues: { heading: '', description: '', values: [] }
    }
  });

  // Field array for values
  const { fields, append, remove } = useFieldArray({
    control,
    name: 'ourValues.values'
  });

  // Reset form when data loads
  useEffect(() => {
    if (aboutUsData?.data?.page) {
      const pageData = aboutUsData.data.page;
      reset({
        ourMission: pageData.ourMission,
        aboutUs: pageData.aboutUs,
        ourVision: pageData.ourVision,
        ourValues: {
          heading: pageData.ourValues.heading,
          description: pageData.ourValues.description,
          values: pageData.ourValues.values.map((value) => ({
            name: value.name,
            description: value.description,
            iconImage: value.iconImage
          }))
        }
      });
    }
  }, [aboutUsData, reset]);

  const handleBack = () => {
    router.push('/dashboard/cms/about-us');
  };

  const onSubmit = async (data: AboutUsFormData) => {
    setIsSubmitting(true);
    updateMutation.mutate(data);
  };

  const addValue = () => {
    append({
      name: '',
      description: '',
      iconImage: ''
    });
  };

  if (aboutUsLoading) {
    return (
      <PageContainer>
        <div className='flex flex-1 flex-col space-y-6'>
          <div className='flex items-center gap-4'>
            <Skeleton className='h-10 w-10' />
            <div>
              <Skeleton className='h-8 w-48' />
              <Skeleton className='mt-2 h-4 w-64' />
            </div>
          </div>
          <div className='space-y-6'>
            {[1, 2, 3, 4].map((i) => (
              <Card key={i}>
                <CardHeader>
                  <Skeleton className='h-6 w-32' />
                </CardHeader>
                <CardContent>
                  <div className='space-y-4'>
                    <Skeleton className='h-10 w-full' />
                    <Skeleton className='h-20 w-full' />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </PageContainer>
    );
  }

  if (!aboutUsData?.data?.page) {
    return (
      <PageContainer>
        <div className='flex flex-1 flex-col items-center justify-center space-y-4'>
          <h2 className='text-2xl font-bold'>No Data Available</h2>
          <p className='text-muted-foreground'>
            About Us page data could not be loaded.
          </p>
          <Button onClick={handleBack}>
            <ArrowLeft className='mr-2 h-4 w-4' />
            Go Back
          </Button>
        </div>
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <div className='flex flex-1 flex-col space-y-6'>
        {/* Header */}
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-4'>
            <Button variant='outline' size='icon' onClick={handleBack}>
              <ArrowLeft className='h-4 w-4' />
            </Button>
            <div>
              <h1 className='text-3xl font-bold'>About Us Page - Edit</h1>
              <p className='text-muted-foreground'>
                Edit all sections of the About Us page
              </p>
            </div>
          </div>
          <Button onClick={handleSubmit(onSubmit)} disabled={isSubmitting}>
            <Save className='mr-2 h-4 w-4' />
            {isSubmitting ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className='space-y-6'>
          {/* Our Mission Section */}
          <Card>
            <CardHeader>
              <CardTitle>Our Mission</CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div>
                <Label htmlFor='ourMission.heading'>Heading</Label>
                <Input
                  id='ourMission.heading'
                  {...register('ourMission.heading')}
                  placeholder='Enter mission heading'
                />
                {errors.ourMission?.heading && (
                  <p className='mt-1 text-sm text-red-500'>
                    {errors.ourMission.heading.message}
                  </p>
                )}
              </div>
              <div>
                <Label htmlFor='ourMission.description'>Description</Label>
                <Textarea
                  id='ourMission.description'
                  {...register('ourMission.description')}
                  placeholder='Enter mission description'
                  rows={4}
                />
                {errors.ourMission?.description && (
                  <p className='mt-1 text-sm text-red-500'>
                    {errors.ourMission.description.message}
                  </p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* About Us Section */}
          <Card>
            <CardHeader>
              <CardTitle>About Us</CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div>
                <Label htmlFor='aboutUs.heading'>Heading</Label>
                <Input
                  id='aboutUs.heading'
                  {...register('aboutUs.heading')}
                  placeholder='Enter about us heading'
                />
                {errors.aboutUs?.heading && (
                  <p className='mt-1 text-sm text-red-500'>
                    {errors.aboutUs.heading.message}
                  </p>
                )}
              </div>
              <div>
                <Label htmlFor='aboutUs.description'>Description</Label>
                <Textarea
                  id='aboutUs.description'
                  {...register('aboutUs.description')}
                  placeholder='Enter about us description'
                  rows={4}
                />
                {errors.aboutUs?.description && (
                  <p className='mt-1 text-sm text-red-500'>
                    {errors.aboutUs.description.message}
                  </p>
                )}
              </div>
              <ImageUploadField
                label='About Us Image'
                value={watch('aboutUs.image') || ''}
                onChange={(url) => setValue('aboutUs.image', url)}
                uploadPath='/uploads/cms/about-us'
                disabled={isSubmitting}
              />
            </CardContent>
          </Card>

          {/* Our Vision Section */}
          <Card>
            <CardHeader>
              <CardTitle>Our Vision</CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div>
                <Label htmlFor='ourVision.heading'>Heading</Label>
                <Input
                  id='ourVision.heading'
                  {...register('ourVision.heading')}
                  placeholder='Enter vision heading'
                />
                {errors.ourVision?.heading && (
                  <p className='mt-1 text-sm text-red-500'>
                    {errors.ourVision.heading.message}
                  </p>
                )}
              </div>
              <div>
                <Label htmlFor='ourVision.description'>Description</Label>
                <Textarea
                  id='ourVision.description'
                  {...register('ourVision.description')}
                  placeholder='Enter vision description'
                  rows={4}
                />
                {errors.ourVision?.description && (
                  <p className='mt-1 text-sm text-red-500'>
                    {errors.ourVision.description.message}
                  </p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Our Values Section */}
          <Card>
            <CardHeader>
              <div className='flex items-center justify-between'>
                <CardTitle>Our Values</CardTitle>
                <Button type='button' onClick={addValue} size='sm'>
                  <Plus className='mr-2 h-4 w-4' />
                  Add Value
                </Button>
              </div>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div>
                <Label htmlFor='ourValues.heading'>Heading</Label>
                <Input
                  id='ourValues.heading'
                  {...register('ourValues.heading')}
                  placeholder='Enter values heading'
                />
                {errors.ourValues?.heading && (
                  <p className='mt-1 text-sm text-red-500'>
                    {errors.ourValues.heading.message}
                  </p>
                )}
              </div>
              <div>
                <Label htmlFor='ourValues.description'>Description</Label>
                <Textarea
                  id='ourValues.description'
                  {...register('ourValues.description')}
                  placeholder='Enter values description'
                  rows={4}
                />
                {errors.ourValues?.description && (
                  <p className='mt-1 text-sm text-red-500'>
                    {errors.ourValues.description.message}
                  </p>
                )}
              </div>

              {/* Values List */}
              <div className='space-y-4'>
                <Label>Values</Label>
                {fields.map((field, index) => (
                  <Card key={field.id} className='border-l-primary border-l-4'>
                    <CardHeader className='pb-3'>
                      <div className='flex items-center justify-between'>
                        <h4 className='text-sm font-medium'>
                          Value {index + 1}
                        </h4>
                        <Button
                          type='button'
                          variant='outline'
                          size='sm'
                          onClick={() => remove(index)}
                        >
                          <Trash2 className='h-4 w-4' />
                        </Button>
                      </div>
                    </CardHeader>
                    <CardContent className='space-y-3'>
                      <div>
                        <Label htmlFor={`ourValues.values.${index}.name`}>
                          Name
                        </Label>
                        <Input
                          id={`ourValues.values.${index}.name`}
                          {...register(`ourValues.values.${index}.name`)}
                          placeholder='Enter value name'
                        />
                        {errors.ourValues?.values?.[index]?.name && (
                          <p className='mt-1 text-sm text-red-500'>
                            {errors.ourValues.values[index]?.name?.message}
                          </p>
                        )}
                      </div>
                      <div>
                        <Label
                          htmlFor={`ourValues.values.${index}.description`}
                        >
                          Description
                        </Label>
                        <Textarea
                          id={`ourValues.values.${index}.description`}
                          {...register(`ourValues.values.${index}.description`)}
                          placeholder='Enter value description'
                          rows={3}
                        />
                        {errors.ourValues?.values?.[index]?.description && (
                          <p className='mt-1 text-sm text-red-500'>
                            {
                              errors.ourValues.values[index]?.description
                                ?.message
                            }
                          </p>
                        )}
                      </div>
                      <ImageUploadField
                        label='Icon Image'
                        value={
                          watch(`ourValues.values.${index}.iconImage`) || ''
                        }
                        onChange={(url) =>
                          setValue(`ourValues.values.${index}.iconImage`, url)
                        }
                        uploadPath='/uploads/cms/about-us/icons'
                        disabled={isSubmitting}
                      />
                    </CardContent>
                  </Card>
                ))}
                {fields.length === 0 && (
                  <div className='text-muted-foreground py-8 text-center'>
                    <p>
                      No values added yet. Click &quot;Add Value&quot; to get
                      started.
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </form>
      </div>
    </PageContainer>
  );
}
