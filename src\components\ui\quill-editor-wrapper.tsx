'use client';

import { QuillEditor } from './quill-editor';
import { Skeleton } from './skeleton';

interface QuillEditorWrapperProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  isLoading?: boolean;
}

export function QuillEditorWrapper({
  value,
  onChange,
  placeholder,
  className,
  disabled,
  isLoading = false
}: QuillEditorWrapperProps) {
  if (isLoading) {
    return (
      <div className='space-y-2'>
        <Skeleton className='h-12 w-full' />
        <Skeleton className='h-48 w-full' />
      </div>
    );
  }
  console.log(value, 'value in wrapper');
  return (
    <QuillEditor
      value={value}
      onChange={onChange}
      placeholder={placeholder}
      className={className}
      disabled={disabled}
    />
  );
}
