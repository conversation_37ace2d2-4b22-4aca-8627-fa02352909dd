'use client';

import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { ArrowLeft } from 'lucide-react';
import { useMemo } from 'react';

import PageContainer from '@/components/layout/page-container';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
// import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@/components/ui/skeleton';

import {
  useGetOpportunitySection,
  useGetTalentSection,
  useGetDiscoverSection,
  useGetCompaniesSection,
  useGetPartnersSection,
  useGetCtaSection,
  useGetAppSection
} from '@/hooks/useQuery';

import type {
  IOpportunitySection,
  ITalentSection,
  IDiscoverSection,
  ICompaniesSection,
  IPartnersSection,
  ICtaSection,
  IAppSection
} from '@/types/query.types';
import Image from 'next/image';

// Types
export type SectionType =
  | 'opportunity'
  | 'talent'
  | 'discover'
  | 'companies'
  | 'partners'
  | 'cta'
  | 'app';

type SectionData =
  | IOpportunitySection
  | ITalentSection
  | IDiscoverSection
  | ICompaniesSection
  | IPartnersSection
  | ICtaSection
  | IAppSection;

// Type guards
function hasSteps(
  data: SectionData
): data is IOpportunitySection | ITalentSection {
  return 'steps' in data;
}

function hasPartners(data: SectionData): data is IPartnersSection {
  return 'partners' in data;
}

function hasImage(
  data: SectionData
): data is IOpportunitySection | ITalentSection | ICtaSection {
  return 'image' in data;
}

function hasSubheading(
  data: SectionData
): data is IDiscoverSection | ICompaniesSection {
  return 'subheading' in data;
}

function hasAppUrls(data: SectionData): data is IAppSection {
  return 'appStoreURL' in data && 'playStoreURL' in data;
}

function hasMetadata(
  data: SectionData
): data is SectionData & { _id?: string; __v?: number } {
  return typeof data === 'object' && data !== null;
}

interface SectionHookResult {
  data: { data: { section: SectionData } } | null | undefined;
  isLoading: boolean;
  error?: any;
}

// Custom hook to handle section data fetching
function useSectionData(section: SectionType): SectionHookResult {
  const opportunityQuery = useGetOpportunitySection();
  const talentQuery = useGetTalentSection();
  const discoverQuery = useGetDiscoverSection();
  const companiesQuery = useGetCompaniesSection();
  const partnersQuery = useGetPartnersSection();
  const ctaQuery = useGetCtaSection();
  const appQuery = useGetAppSection();

  const selectedQuery = useMemo(() => {
    switch (section) {
      case 'opportunity':
        return opportunityQuery;
      case 'talent':
        return talentQuery;
      case 'discover':
        return discoverQuery;
      case 'companies':
        return companiesQuery;
      case 'partners':
        return partnersQuery;
      case 'cta':
        return ctaQuery;
      case 'app':
        return appQuery;
      default:
        return { data: null, isLoading: false, error: null };
    }
  }, [
    section,
    opportunityQuery,
    talentQuery,
    discoverQuery,
    companiesQuery,
    partnersQuery,
    ctaQuery,
    appQuery
  ]);

  return {
    data: selectedQuery.data || null,
    isLoading: selectedQuery.isLoading || false,
    error: selectedQuery.error || null
  };
}

// Section configuration
const SECTION_CONFIG: Record<
  SectionType,
  { title: string; description: string }
> = {
  opportunity: {
    title: 'Opportunity Section',
    description: 'View the current content of this section'
  },
  talent: {
    title: 'Talent Section',
    description: 'View the current content of this section'
  },
  discover: {
    title: 'Discover Section',
    description: 'View the current content of this section'
  },
  companies: {
    title: 'Companies Section',
    description: 'View the current content of this section'
  },
  partners: {
    title: 'Partners Section',
    description: 'View the current content of this section'
  },
  cta: {
    title: 'Call to Action Section',
    description: 'View the current content of this section'
  },
  app: {
    title: 'Mobile App Section',
    description: 'View the current content of this section'
  }
} as const;

export default function ViewSectionPage() {
  const params = useParams();
  const router = useRouter();
  const section = params.section as SectionType;

  const { data, isLoading } = useSectionData(section);
  const sectionData = data?.data?.section;
  const config = SECTION_CONFIG[section];

  const handleBack = () => {
    router.push('/dashboard/cms/home');
  };

  const handleEdit = () => {
    router.push(`/dashboard/cms/home/<USER>/edit`);
  };

  if (isLoading) {
    return (
      <PageContainer>
        <div className='flex flex-1 flex-col space-y-6'>
          <div className='flex items-center gap-4'>
            <Skeleton className='h-10 w-10' />
            <div className='space-y-2'>
              <Skeleton className='h-8 w-64' />
              <Skeleton className='h-4 w-48' />
            </div>
          </div>
          <div className='space-y-4'>
            <Skeleton className='h-32 w-full' />
            <Skeleton className='h-32 w-full' />
            <Skeleton className='h-32 w-full' />
          </div>
        </div>
      </PageContainer>
    );
  }

  if (!sectionData) {
    return (
      <PageContainer>
        <div className='flex flex-1 flex-col space-y-6'>
          <div className='flex items-center gap-4'>
            <Button variant='ghost' size='sm' onClick={handleBack}>
              <ArrowLeft className='mr-2 h-4 w-4' />
              Back to CMS Home
            </Button>
          </div>
          <div className='py-12 text-center'>
            <h2 className='mb-2 text-2xl font-bold'>No Data Available</h2>
            <p className='text-muted-foreground'>
              This section doesn&apos;t have any data yet.
            </p>
          </div>
        </div>
      </PageContainer>
    );
  }

  const renderBasicFields = () => (
    <div className='space-y-4'>
      <div>
        <h4 className='mb-2 text-sm font-medium'>Heading</h4>
        <p className='text-muted-foreground bg-muted rounded-md p-3 text-sm'>
          {sectionData?.heading}
        </p>
      </div>
      <div>
        <h4 className='mb-2 text-sm font-medium'>Description</h4>
        <p className='text-muted-foreground bg-muted rounded-md p-3 text-sm'>
          {sectionData?.description}
        </p>
      </div>
    </div>
  );

  const renderSteps = () => {
    if (!sectionData || !hasSteps(sectionData) || !sectionData.steps?.length) {
      return null;
    }

    return (
      <div>
        <h4 className='mb-3 text-sm font-medium'>
          Steps ({sectionData.steps.length})
        </h4>
        <div className='space-y-3'>
          {sectionData.steps.map((step, index: number) => (
            <Card key={index} className='border-l-primary border-l-4'>
              <CardContent className='pt-4'>
                <div className='space-y-2'>
                  <div className='flex items-center gap-2'>
                    <Badge variant='outline'>Step {index + 1}</Badge>
                    <h5 className='font-medium'>{step.heading}</h5>
                  </div>
                  <p className='text-muted-foreground text-sm'>
                    {step.description}
                  </p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  };

  const renderPartners = () => {
    if (
      !sectionData ||
      !hasPartners(sectionData) ||
      !sectionData.partners?.length
    ) {
      return null;
    }

    return (
      <div>
        <h4 className='mb-3 text-sm font-medium'>
          Partners ({sectionData.partners.length})
        </h4>
        <div className='grid grid-cols-1 gap-3 sm:grid-cols-2'>
          {sectionData.partners.map((partner, index: number) => (
            <Card key={index}>
              <CardContent className='pt-4'>
                <div className='space-y-2'>
                  <h5 className='font-medium'>{partner.name}</h5>
                  <div className='text-muted-foreground text-sm'>
                    <span className='font-medium'>Image URL:</span>
                    {/* <p className='bg-muted mt-1 rounded p-2 text-xs break-all'>
                      {partner.imageURL}
                    </p> */}
                    <Image
                      src={partner?.name}
                      alt={partner?.imageURL}
                      width={500}
                      height={300}
                      className='mt-4 rounded-lg object-cover'
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  };

  const renderImage = () => {
    if (!sectionData || !hasImage(sectionData)) return null;

    return (
      <div>
        <h4 className='mb-2 text-sm font-medium'>Image</h4>
        <div className='bg-muted rounded-md p-3'>
          {/* <p className='text-muted-foreground text-sm break-all'>
            {sectionData.image}
          </p> */}
          <Image
            src={sectionData.image}
            alt={sectionData.heading}
            width={500}
            height={300}
            className='mt-4 rounded-lg object-cover'
          />
        </div>
      </div>
    );
  };

  const renderSubheading = () => {
    if (!sectionData || !hasSubheading(sectionData)) return null;

    return (
      <div>
        <h4 className='mb-2 text-sm font-medium'>Subheading</h4>
        <p className='text-muted-foreground bg-muted rounded-md p-3 text-sm'>
          {sectionData.subheading}
        </p>
      </div>
    );
  };

  const renderAppUrls = () => {
    if (!sectionData || !hasAppUrls(sectionData)) return null;

    return (
      <div className='space-y-4'>
        <div>
          <h4 className='mb-2 text-sm font-medium'>App Store URL</h4>
          <div className='bg-muted rounded-md p-3'>
            <p className='text-muted-foreground text-sm break-all'>
              {sectionData.appStoreURL}
            </p>
          </div>
        </div>
        <div>
          <h4 className='mb-2 text-sm font-medium'>Play Store URL</h4>
          <div className='bg-muted rounded-md p-3'>
            <p className='text-muted-foreground text-sm break-all'>
              {sectionData.playStoreURL}
            </p>
          </div>
        </div>
      </div>
    );
  };

  return (
    <PageContainer>
      <div className='flex flex-1 flex-col space-y-6'>
        {/* Header */}
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-4'>
            <Button variant='ghost' size='sm' onClick={handleBack}>
              <ArrowLeft className='mr-2 h-4 w-4' />
              Back to CMS Home
            </Button>
            <div>
              <div className='flex items-center gap-2'>
                <h1 className='text-3xl font-bold'>{config.title}</h1>
                <Badge variant='outline'>{section}</Badge>
              </div>
              <p className='text-muted-foreground'>{config.description}</p>
            </div>
          </div>
          <Button onClick={handleEdit}>Edit Section</Button>
        </div>

        {/* Content */}
        <Card>
          <CardHeader>
            <CardTitle>Section Content</CardTitle>
          </CardHeader>
          <CardContent>
            <div className='space-y-6'>
              {renderBasicFields()}

              {renderSubheading()}

              {renderImage()}

              {renderAppUrls()}

              {sectionData &&
                hasSteps(sectionData) &&
                sectionData.steps?.length && (
                  <>
                    <Separator />
                    {renderSteps()}
                  </>
                )}

              {sectionData &&
                hasPartners(sectionData) &&
                sectionData.partners?.length && (
                  <>
                    <Separator />
                    {renderPartners()}
                  </>
                )}

              {sectionData && hasMetadata(sectionData) && (
                <>
                  <Separator />
                  <div className='text-muted-foreground space-y-1 text-xs'>
                    {(sectionData as any)._id && (
                      <p>
                        <span className='font-medium'>ID:</span>{' '}
                        {(sectionData as any)._id}
                      </p>
                    )}
                    {(sectionData as any).__v !== undefined && (
                      <p>
                        <span className='font-medium'>Version:</span>{' '}
                        {(sectionData as any).__v}
                      </p>
                    )}
                  </div>
                </>
              )}
            </div>
            {/* <ScrollArea className='max-h-[70vh] pr-4'>
            </ScrollArea> */}
          </CardContent>
        </Card>
      </div>
    </PageContainer>
  );
}
