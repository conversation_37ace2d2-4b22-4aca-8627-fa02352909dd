'use client';

import { useRouter } from 'next/navigation';
import PageContainer from '@/components/layout/page-container';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { ArrowLeft, Edit } from 'lucide-react';
import { useGetHowItWorksPage } from '@/hooks/useQuery';
import Image from 'next/image';
import { DEFAULT_IMAGE } from '@/constants/app.const';

export default function HowItWorksViewPage() {
  const router = useRouter();

  // Fetch How It Works page data
  const { data: howItWorksData, isLoading: howItWorksLoading } =
    useGetHowItWorksPage();

  const handleBack = () => {
    router.push('/dashboard/cms/how-it-works');
  };

  const handleEdit = () => {
    router.push('/dashboard/cms/how-it-works/edit');
  };

  if (howItWorksLoading) {
    return (
      <PageContainer>
        <div className='flex flex-1 flex-col space-y-6'>
          <div className='flex items-center gap-4'>
            <Skeleton className='h-10 w-10' />
            <div>
              <Skeleton className='h-8 w-48' />
              <Skeleton className='mt-2 h-4 w-64' />
            </div>
          </div>
          <div className='space-y-6'>
            {[1, 2, 3, 4].map((i) => (
              <Card key={i}>
                <CardHeader>
                  <Skeleton className='h-6 w-32' />
                </CardHeader>
                <CardContent>
                  <Skeleton className='h-20 w-full' />
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </PageContainer>
    );
  }

  const pageData = howItWorksData?.data?.page;

  if (!pageData) {
    return (
      <PageContainer>
        <div className='flex flex-1 flex-col items-center justify-center space-y-4'>
          <h2 className='text-2xl font-bold'>No Data Available</h2>
          <p className='text-muted-foreground'>
            How It Works page data could not be loaded.
          </p>
          <Button onClick={handleBack}>
            <ArrowLeft className='mr-2 h-4 w-4' />
            Go Back
          </Button>
        </div>
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <div className='flex flex-1 flex-col space-y-6'>
        {/* Header */}
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-4'>
            <Button variant='outline' size='icon' onClick={handleBack}>
              <ArrowLeft className='h-4 w-4' />
            </Button>
            <div>
              <h1 className='text-3xl font-bold'>How It Works Page - View</h1>
              <p className='text-muted-foreground'>
                View all sections of the How It Works page
              </p>
            </div>
          </div>
          <Button onClick={handleEdit}>
            <Edit className='mr-2 h-4 w-4' />
            Edit Page
          </Button>
        </div>

        {/* Main Section */}
        <Card>
          <CardHeader>
            <div className='flex items-center justify-between'>
              <CardTitle className='text-xl'>Main Section</CardTitle>
              <Badge variant='secondary'>main-section</Badge>
            </div>
          </CardHeader>
          <CardContent className='space-y-4'>
            <div>
              <h3 className='mb-2 text-lg font-semibold'>{pageData.heading}</h3>
              <p className='text-muted-foreground mb-4 leading-relaxed'>
                {pageData.description}
              </p>
              {pageData.image && (
                <div className='relative h-64 w-full overflow-hidden rounded-lg'>
                  <Image
                    src={pageData.image || DEFAULT_IMAGE}
                    alt='How It Works'
                    fill
                    className='object-cover'
                  />
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* For Candidates Section */}
        <Card>
          <CardHeader>
            <div className='flex items-center justify-between'>
              <CardTitle className='text-xl'>For Candidates</CardTitle>
              <Badge variant='secondary'>for-candidates</Badge>
            </div>
          </CardHeader>
          <CardContent className='space-y-4'>
            <div>
              <h3 className='mb-2 text-lg font-semibold'>
                {pageData.forCandidates.heading}
              </h3>
              <p className='text-muted-foreground mb-4 leading-relaxed'>
                {pageData.forCandidates.description}
              </p>

              {pageData.forCandidates.steps &&
                pageData.forCandidates.steps.length > 0 && (
                  <div className='space-y-3'>
                    <h4 className='font-medium'>Steps:</h4>
                    <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
                      {pageData.forCandidates.steps.map((step, index) => (
                        <Card
                          key={step._id || index}
                          className='border-l-4 border-l-green-500'
                        >
                          <CardContent className='p-4'>
                            <div className='flex items-start gap-3'>
                              <div className='flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full bg-green-100 text-sm font-semibold text-green-800'>
                                {index + 1}
                              </div>
                              <div className='flex-1'>
                                <h5 className='mb-1 font-semibold'>
                                  {step.heading}
                                </h5>
                                <p className='text-muted-foreground text-sm'>
                                  {step.description}
                                </p>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </div>
                )}
            </div>
          </CardContent>
        </Card>

        {/* For Recruiters Section */}
        <Card>
          <CardHeader>
            <div className='flex items-center justify-between'>
              <CardTitle className='text-xl'>For Recruiters</CardTitle>
              <Badge variant='outline'>for-recruiters</Badge>
            </div>
          </CardHeader>
          <CardContent className='space-y-4'>
            <div>
              <h3 className='mb-2 text-lg font-semibold'>
                {pageData.forRecruiters.heading}
              </h3>
              <p className='text-muted-foreground mb-4 leading-relaxed'>
                {pageData.forRecruiters.description}
              </p>

              {pageData.forRecruiters.steps &&
                pageData.forRecruiters.steps.length > 0 && (
                  <div className='space-y-3'>
                    <h4 className='font-medium'>Steps:</h4>
                    <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
                      {pageData.forRecruiters.steps.map((step, index) => (
                        <Card
                          key={step._id || index}
                          className='border-l-4 border-l-purple-500'
                        >
                          <CardContent className='p-4'>
                            <div className='flex items-start gap-3'>
                              <div className='flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full bg-purple-100 text-sm font-semibold text-purple-800'>
                                {index + 1}
                              </div>
                              <div className='flex-1'>
                                <h5 className='mb-1 font-semibold'>
                                  {step.heading}
                                </h5>
                                <p className='text-muted-foreground text-sm'>
                                  {step.description}
                                </p>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </div>
                )}
            </div>
          </CardContent>
        </Card>

        {/* Benefits Section */}
        <Card>
          <CardHeader>
            <div className='flex items-center justify-between'>
              <CardTitle className='text-xl'>Benefits</CardTitle>
              <Badge variant='secondary'>benefits</Badge>
            </div>
          </CardHeader>
          <CardContent className='space-y-4'>
            {pageData.benefits && pageData.benefits.length > 0 && (
              <div className='grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3'>
                {pageData.benefits.map((benefit, index) => (
                  <Card
                    key={benefit._id || index}
                    className='border-l-4 border-l-orange-500'
                  >
                    <CardContent className='p-4'>
                      <div className='flex items-start gap-3'>
                        {benefit.iconImage && (
                          <div className='relative h-12 w-12 flex-shrink-0 overflow-hidden rounded-lg'>
                            <Image
                              src={benefit.iconImage || DEFAULT_IMAGE}
                              alt={benefit.heading}
                              fill
                              className='object-cover'
                            />
                          </div>
                        )}
                        <div className='flex-1'>
                          <h4 className='mb-1 font-semibold'>
                            {benefit.heading}
                          </h4>
                          <p className='text-muted-foreground text-sm'>
                            {benefit.description}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </PageContainer>
  );
}
