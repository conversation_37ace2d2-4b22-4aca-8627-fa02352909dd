'use client';

import { useState } from 'react';
import { useGetCategories } from '@/hooks/useQuery';
import { useDeleteCategory, useAddCategories } from '@/hooks/useMutation';
import { useUpdateCategory } from '@/hooks/useQuery';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Pagination } from '@/components/ui/pagination';
import {
  PaginationContent,
  PaginationItem,
  PaginationNext,
  PaginationPrevious
} from '@/components/ui/pagination';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger
} from '@/components/ui/alert-dialog';
import {
  Di<PERSON>,
  Dialog<PERSON>ontent,
  Dialog<PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogFooter
} from '@/components/ui/dialog';
import { ICategory } from '@/types/query.types';
import PageContainer from '@/components/layout/page-container';
import { toast } from 'sonner';
import { ApiError } from '@/types/common.types';

export default function CategoriesPage() {
  const [searchInput, setSearchInput] = useState(''); // State for the input field
  const [searchQuery, setSearchQuery] = useState(''); // State for the actual search query
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [categoryToDelete, setCategoryToDelete] = useState<ICategory | null>(
    null
  );
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [categoryToEdit, setCategoryToEdit] = useState<ICategory | null>(null);
  const [editedCategoryName, setEditedCategoryName] = useState('');
  const [isAddCategoryDialogOpen, setIsAddCategoryDialogOpen] = useState(false);
  const [newCategoryName, setNewCategoryName] = useState('');

  const { data, isLoading, isError } = useGetCategories({
    search: searchQuery, // Use searchQuery for the API call
    page,
    limit
  });

  const { mutate: deleteCategoryMutation, isPending: isDeleting } =
    useDeleteCategory();
  const { mutate: updateCategoryMutation, isPending: isUpdating } =
    useUpdateCategory();
  const { mutate: addCategoriesMutation, isPending: isAddingCategory } =
    useAddCategories();

  const categories = data?.data?.categories || [];
  const totalPages = data?.data?.pagination.pages || 1;

  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchInput(e.target.value);
  };

  const handleSearchClick = () => {
    setSearchQuery(searchInput);
    setPage(1); // Reset to first page on search
  };

  const handleClearSearch = () => {
    setSearchInput('');
    setSearchQuery('');
    setPage(1); // Reset to first page on clear
  };

  const handlePreviousPage = () => {
    setPage((prev) => Math.max(prev - 1, 1));
  };

  const handleFirstPage = () => {
    setPage(1);
  };

  const handleLastPage = () => {
    setPage(totalPages);
  };

  const handleNextPage = () => {
    setPage((prev) => Math.min(prev + 1, totalPages));
  };

  const handleDeleteClick = (category: ICategory) => {
    setCategoryToDelete(category);
    setIsDeleteDialogOpen(true);
  };

  const handleConfirmDelete = () => {
    if (categoryToDelete) {
      deleteCategoryMutation(categoryToDelete._id, {
        onSuccess: () => {
          setIsDeleteDialogOpen(false);
          setCategoryToDelete(null);
        },
        onError: () => {
          setIsDeleteDialogOpen(false);
          setCategoryToDelete(null);
        }
      });
    }
  };

  const handleEditClick = (category: ICategory) => {
    setCategoryToEdit(category);
    setEditedCategoryName(category.category);
    setIsEditDialogOpen(true);
  };

  const handleUpdateCategory = () => {
    if (categoryToEdit) {
      updateCategoryMutation(
        {
          categoryId: categoryToEdit._id,
          payload: {
            category: editedCategoryName,
            isEMSI: categoryToEdit.isEMSI
          }
        },
        {
          onSuccess: () => {
            setIsEditDialogOpen(false);
            setCategoryToEdit(null);
            setEditedCategoryName('');
            toast.success('Category updated successfully');
          },
          onError: () => {
            setIsEditDialogOpen(false);
            setCategoryToEdit(null);
            setEditedCategoryName('');
            toast.error('Failed to update category');
          }
        }
      );
    }
  };

  const handleAddCategory = () => {
    if (newCategoryName.trim()) {
      addCategoriesMutation(
        { categories: [newCategoryName.trim()] },
        {
          onSuccess: () => {
            toast.success('Category added successfully');
            setNewCategoryName('');
            setIsAddCategoryDialogOpen(false);
          },
          onError: (error: ApiError) => {
            toast.error(
              error.response?.data?.message || 'Failed to add category'
            );
          }
        }
      );
    } else {
      toast.error('Category name cannot be empty');
    }
  };

  const getPaginationItems = () => {
    const items = [];
    const maxPagesToShow = 5; // Number of page numbers to show directly

    if (totalPages <= maxPagesToShow) {
      for (let i = 1; i <= totalPages; i++) {
        items.push(
          <PaginationItem key={i}>
            <Button
              variant={page === i ? 'default' : 'outline'}
              onClick={() => setPage(i)}
            >
              {i}
            </Button>
          </PaginationItem>
        );
      }
    } else {
      // Always show first page
      items.push(
        <PaginationItem key={1}>
          <Button
            variant={page === 1 ? 'default' : 'outline'}
            onClick={() => setPage(1)}
          >
            1
          </Button>
        </PaginationItem>
      );

      // Logic for dots and middle pages
      const startPage = Math.max(2, page - Math.floor(maxPagesToShow / 2) + 1);
      const endPage = Math.min(
        totalPages - 1,
        page + Math.floor(maxPagesToShow / 2) - 1
      );

      if (startPage > 2) {
        items.push(<PaginationItem key='dots-start'>...</PaginationItem>);
      }

      for (let i = startPage; i <= endPage; i++) {
        items.push(
          <PaginationItem key={i}>
            <Button
              variant={page === i ? 'default' : 'outline'}
              onClick={() => setPage(i)}
            >
              {i}
            </Button>
          </PaginationItem>
        );
      }

      if (endPage < totalPages - 1) {
        items.push(<PaginationItem key='dots-end'>...</PaginationItem>);
      }

      // Always show last page
      items.push(
        <PaginationItem key={totalPages}>
          <Button
            variant={page === totalPages ? 'default' : 'outline'}
            onClick={() => setPage(totalPages)}
          >
            {totalPages}
          </Button>
        </PaginationItem>
      );
    }
    return items;
  };

  if (isLoading) return <PageContainer>Loading categories...</PageContainer>;
  if (isError) return <PageContainer>Error loading categories.</PageContainer>;

  return (
    <PageContainer>
      <div className='container mx-auto py-10'>
        <h1 className='mb-6 text-3xl font-bold'>Categories</h1>
        <div className='mb-4 flex items-center'>
          <Input
            placeholder='Search categories...'
            value={searchInput}
            onChange={handleSearchInputChange}
            className='max-w-sm'
          />
          <Button onClick={handleSearchClick} className='ml-2'>
            Search
          </Button>
          {searchQuery && (
            <Button
              onClick={handleClearSearch}
              variant='outline'
              className='ml-2'
            >
              Clear Search
            </Button>
          )}
          <div className='ml-auto'>
            <Button onClick={() => setIsAddCategoryDialogOpen(true)}>
              Add New Category
            </Button>
          </div>
        </div>
        <div className='rounded-md border'>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>NO</TableHead>
                <TableHead>Name</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {categories.length > 0 ? (
                categories.map((category: ICategory, index: number) => (
                  <TableRow key={category._id}>
                    <TableCell className='font-medium'>{index + 1}</TableCell>
                    <TableCell>{category.category}</TableCell>
                    <TableCell>
                      <div className='flex gap-x-2'>
                        <Button
                          variant='outline'
                          onClick={() => handleEditClick(category)}
                        >
                          Edit
                        </Button>
                        <Button
                          variant='destructive'
                          onClick={() => handleDeleteClick(category)}
                          disabled={isDeleting}
                        >
                          Delete
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={3} className='h-24 text-center'>
                    No categories found.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
        <div className='flex items-center justify-end space-x-2 py-4'>
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <Button
                  variant='outline'
                  onClick={handleFirstPage}
                  disabled={page === 1}
                >
                  First
                </Button>
              </PaginationItem>
              <PaginationItem>
                <PaginationPrevious
                  onClick={handlePreviousPage}
                  // disabled={page === 1}
                />
              </PaginationItem>
              {getPaginationItems()}
              <PaginationItem>
                <PaginationNext
                  onClick={handleNextPage}
                  // disabled={page === totalPages}
                />
              </PaginationItem>
              <PaginationItem>
                <Button
                  variant='outline'
                  onClick={handleLastPage}
                  disabled={page === totalPages}
                >
                  Last
                </Button>
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      </div>

      <AlertDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the{' '}
              <strong>{categoryToDelete?.category}</strong> category.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmDelete}
              disabled={isDeleting}
            >
              {isDeleting ? 'Deleting...' : 'Yes, delete category'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Category</DialogTitle>
          </DialogHeader>
          <div className='grid gap-4 py-4'>
            <div className='grid grid-cols-4 items-center gap-4'>
              <label htmlFor='categoryName' className='text-right'>
                Category Name
              </label>
              <Input
                id='categoryName'
                value={editedCategoryName}
                onChange={(e) => setEditedCategoryName(e.target.value)}
                className='col-span-3'
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant='outline'
              onClick={() => setIsEditDialogOpen(false)}
              disabled={isUpdating}
            >
              Cancel
            </Button>
            <Button onClick={handleUpdateCategory} disabled={isUpdating}>
              {isUpdating ? 'Updating...' : 'Save Changes'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog
        open={isAddCategoryDialogOpen}
        onOpenChange={setIsAddCategoryDialogOpen}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add New Category</DialogTitle>
          </DialogHeader>
          <div className='grid gap-4 py-4'>
            <div className='grid grid-cols-4 items-center gap-4'>
              <label htmlFor='newCategoryName' className='text-right'>
                Category Name
              </label>
              <Input
                id='newCategoryName'
                value={newCategoryName}
                onChange={(e) => setNewCategoryName(e.target.value)}
                className='col-span-3'
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant='outline'
              onClick={() => setIsAddCategoryDialogOpen(false)}
              disabled={isAddingCategory}
            >
              Cancel
            </Button>
            <Button onClick={handleAddCategory} disabled={isAddingCategory}>
              {isAddingCategory ? 'Adding...' : 'Add Category'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </PageContainer>
  );
}
