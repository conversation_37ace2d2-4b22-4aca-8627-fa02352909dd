import { useMutation, UseMutationOptions } from '@tanstack/react-query';
import { toast } from 'sonner';
import axiosInstance from '@/lib/axios';
import { ApiError } from '@/types/common.types';

// Alternative approach: Let backend handle S3 upload entirely
interface IDirectUploadVariables {
  file: File;
  path: string;
}

interface IDirectUploadResponse {
  success: boolean;
  message: string;
  data: {
    url: string;
    key: string;
  };
}

const uploadFileToBackend = async ({
  file,
  path
}: IDirectUploadVariables): Promise<{ url: string }> => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('path', path);

  const { data } = await axiosInstance.post<IDirectUploadResponse>(
    '/uploader/upload', // This endpoint should handle the entire S3 upload process
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }
  );

  return { url: data.data.url };
};

export const useDirectS3Upload = <TError = ApiError, TContext = unknown>(
  options?: UseMutationOptions<
    { url: string },
    TError,
    IDirectUploadVariables,
    TContext
  >
) => {
  const { mutateAsync, isPending, ...props } = useMutation<
    { url: string },
    TError,
    IDirectUploadVariables,
    TContext
  >({
    mutationKey: ['direct-s3-upload'],
    mutationFn: uploadFileToBackend,
    onSuccess: () => {
      toast.success('Image uploaded successfully');
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message || 'Failed to upload image'
      );
    },
    ...options
  });

  return { upload: mutateAsync, isUploading: isPending, ...props };
};
