'use client';

import { useState } from 'react';
import { useGetSkills } from '@/hooks/useQuery';
import {
  useAddSkills,
  useDeleteSkill,
  useUpdateSkill
} from '@/hooks/useMutation';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Pagination } from '@/components/ui/pagination';
import {
  PaginationContent,
  PaginationItem,
  PaginationNext,
  PaginationPrevious
} from '@/components/ui/pagination';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger
} from '@/components/ui/alert-dialog';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from '@/components/ui/dialog';
import { ISkill } from '@/types/query.types';
import PageContainer from '@/components/layout/page-container';
import { toast } from 'sonner';

export default function SkillsPage() {
  const [searchInput, setSearchInput] = useState(''); // State for the input field
  const [searchQuery, setSearchQuery] = useState(''); // State for the actual search query
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [skillToDelete, setSkillToDelete] = useState<ISkill | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [skillToEdit, setSkillToEdit] = useState<ISkill | null>(null);
  const [editedSkillName, setEditedSkillName] = useState('');
  const [isAddSkillDialogOpen, setIsAddSkillDialogOpen] = useState(false);
  const [newSkillName, setNewSkillName] = useState('');

  const { data, isLoading, isError } = useGetSkills({
    search: searchQuery, // Use searchQuery for the API call
    page,
    limit
  });

  const { mutate: deleteSkillMutation, isPending: isDeleting } =
    useDeleteSkill();
  const { mutate: updateSkillMutation, isPending: isUpdating } =
    useUpdateSkill();

  const skills = data?.data?.skills || [];
  const totalPages = data?.data?.pagination.pages || 1;

  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchInput(e.target.value);
  };

  const handleSearchClick = () => {
    setSearchQuery(searchInput);
    setPage(1); // Reset to first page on search
  };

  const handleClearSearch = () => {
    setSearchInput('');
    setSearchQuery('');
    setPage(1); // Reset to first page on clear
  };

  const handlePreviousPage = () => {
    setPage((prev) => Math.max(prev - 1, 1));
  };

  const handleFirstPage = () => {
    setPage(1);
  };

  const handleLastPage = () => {
    setPage(totalPages);
  };

  const handleNextPage = () => {
    setPage((prev) => Math.min(prev + 1, totalPages));
  };

  const handleDeleteClick = (skill: ISkill) => {
    setSkillToDelete(skill);
    setIsDeleteDialogOpen(true);
  };

  const handleConfirmDelete = () => {
    if (skillToDelete) {
      deleteSkillMutation(skillToDelete._id, {
        onSuccess: () => {
          setIsDeleteDialogOpen(false);
          setSkillToDelete(null);
        },
        onError: () => {
          setIsDeleteDialogOpen(false);
          setSkillToDelete(null);
        }
      });
    }
  };

  const handleEditClick = (skill: ISkill) => {
    setSkillToEdit(skill);
    setEditedSkillName(skill.skill);
    setIsEditDialogOpen(true);
  };

  const handleUpdateSkill = () => {
    if (skillToEdit) {
      updateSkillMutation(
        {
          skillId: skillToEdit._id,
          data: {
            skill: editedSkillName,
            isEMSI: skillToEdit.isEMSI
          }
        },
        {
          onSuccess: () => {
            setIsEditDialogOpen(false);
            setSkillToEdit(null);
            setEditedSkillName('');
            toast.success('Skill updated successfully');
          },
          onError: () => {
            setIsEditDialogOpen(false);
            setSkillToEdit(null);
            setEditedSkillName('');
            toast.error('Failed to update skill');
          }
        }
      );
    }
  };

  const { mutate: addSkillsMutation, isPending: isAddingSkill } =
    useAddSkills();

  const handleAddSkill = () => {
    if (newSkillName.trim()) {
      addSkillsMutation(
        { skills: [newSkillName.trim()] },
        {
          onSuccess: () => {
            toast.success('Skill added successfully');
            setNewSkillName('');
            setIsAddSkillDialogOpen(false);
          },
          onError: (error) => {
            toast.error(error.response?.data?.message || 'Failed to add skill');
          }
        }
      );
    } else {
      toast.error('Skill name cannot be empty');
    }
  };

  const getPaginationItems = () => {
    const items = [];
    const maxPagesToShow = 5; // Number of page numbers to show directly

    if (totalPages <= maxPagesToShow) {
      for (let i = 1; i <= totalPages; i++) {
        items.push(
          <PaginationItem key={i}>
            <Button
              variant={page === i ? 'default' : 'outline'}
              onClick={() => setPage(i)}
            >
              {i}
            </Button>
          </PaginationItem>
        );
      }
    } else {
      // Always show first page
      items.push(
        <PaginationItem key={1}>
          <Button
            variant={page === 1 ? 'default' : 'outline'}
            onClick={() => setPage(1)}
          >
            1
          </Button>
        </PaginationItem>
      );

      // Logic for dots and middle pages
      const startPage = Math.max(2, page - Math.floor(maxPagesToShow / 2) + 1);
      const endPage = Math.min(
        totalPages - 1,
        page + Math.floor(maxPagesToShow / 2) - 1
      );

      if (startPage > 2) {
        items.push(<PaginationItem key='dots-start'>...</PaginationItem>);
      }

      for (let i = startPage; i <= endPage; i++) {
        items.push(
          <PaginationItem key={i}>
            <Button
              variant={page === i ? 'default' : 'outline'}
              onClick={() => setPage(i)}
            >
              {i}
            </Button>
          </PaginationItem>
        );
      }

      if (endPage < totalPages - 1) {
        items.push(<PaginationItem key='dots-end'>...</PaginationItem>);
      }

      // Always show last page
      items.push(
        <PaginationItem key={totalPages}>
          <Button
            variant={page === totalPages ? 'default' : 'outline'}
            onClick={() => setPage(totalPages)}
          >
            {totalPages}
          </Button>
        </PaginationItem>
      );
    }
    return items;
  };

  if (isLoading) return <PageContainer>Loading skills...</PageContainer>;
  if (isError) return <PageContainer>Error loading skills.</PageContainer>;

  return (
    <PageContainer>
      <div className='container mx-auto py-10'>
        <h1 className='mb-6 text-3xl font-bold'>Skills</h1>
        <div className='mb-4 flex items-center'>
          <Input
            placeholder='Search skills...'
            value={searchInput}
            onChange={handleSearchInputChange}
            className='max-w-sm'
          />
          <Button onClick={handleSearchClick} className='ml-2'>
            Search
          </Button>
          {searchQuery && (
            <Button
              onClick={handleClearSearch}
              variant='outline'
              className='ml-2'
            >
              Clear Search
            </Button>
          )}
          <div className='ml-auto'>
            <Button onClick={() => setIsAddSkillDialogOpen(true)}>
              Add New Skill
            </Button>
          </div>
        </div>
        <div className='rounded-md border'>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>NO</TableHead>
                <TableHead>Name</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {skills.length > 0 ? (
                skills.map((skill: ISkill, index: number) => (
                  <TableRow key={skill._id}>
                    <TableCell className='font-medium'>{index + 1}</TableCell>
                    <TableCell>{skill.skill}</TableCell>
                    <TableCell>
                      <div className='flex gap-x-2'>
                        <Button
                          variant='outline'
                          onClick={() => handleEditClick(skill)}
                        >
                          Edit
                        </Button>
                        <Button
                          variant='destructive'
                          onClick={() => handleDeleteClick(skill)}
                          disabled={isDeleting}
                        >
                          Delete
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={3} className='h-24 text-center'>
                    No skills found.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
        <div className='flex items-center justify-end space-x-2 py-4'>
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <Button
                  variant='outline'
                  onClick={handleFirstPage}
                  disabled={page === 1}
                >
                  First
                </Button>
              </PaginationItem>
              <PaginationItem>
                <PaginationPrevious
                  onClick={handlePreviousPage}
                  // disabled={page === 1}
                />
              </PaginationItem>
              {getPaginationItems()}
              <PaginationItem>
                <PaginationNext
                  onClick={handleNextPage}
                  // disabled={page === totalPages}
                />
              </PaginationItem>
              <PaginationItem>
                <Button
                  variant='outline'
                  onClick={handleLastPage}
                  disabled={page === totalPages}
                >
                  Last
                </Button>
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      </div>

      <AlertDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the{' '}
              <strong>{skillToDelete?.skill}</strong> skill.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmDelete}
              disabled={isDeleting}
            >
              {isDeleting ? 'Deleting...' : 'Yes, delete skill'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Skill</DialogTitle>
          </DialogHeader>
          <div className='grid gap-4 py-4'>
            <div className='grid grid-cols-4 items-center gap-4'>
              <label htmlFor='skillName' className='text-right'>
                Skill Name
              </label>
              <Input
                id='skillName'
                value={editedSkillName}
                onChange={(e) => setEditedSkillName(e.target.value)}
                className='col-span-3'
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant='outline'
              onClick={() => setIsEditDialogOpen(false)}
              disabled={isUpdating}
            >
              Cancel
            </Button>
            <Button onClick={handleUpdateSkill} disabled={isUpdating}>
              {isUpdating ? 'Updating...' : 'Save Changes'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog
        open={isAddSkillDialogOpen}
        onOpenChange={setIsAddSkillDialogOpen}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add New Skill</DialogTitle>
          </DialogHeader>
          <div className='grid gap-4 py-4'>
            <div className='grid grid-cols-4 items-center gap-4'>
              <label htmlFor='newSkillName' className='text-right'>
                Skill Name
              </label>
              <Input
                id='newSkillName'
                value={newSkillName}
                onChange={(e) => setNewSkillName(e.target.value)}
                className='col-span-3'
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant='outline'
              onClick={() => setIsAddSkillDialogOpen(false)}
              disabled={isAddingSkill}
            >
              Cancel
            </Button>
            <Button onClick={handleAddSkill} disabled={isAddingSkill}>
              {isAddingSkill ? 'Adding...' : 'Add Skill'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </PageContainer>
  );
}
