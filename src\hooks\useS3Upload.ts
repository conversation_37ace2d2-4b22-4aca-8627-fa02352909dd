import { useMutation, UseMutationOptions } from '@tanstack/react-query';
import { toast } from 'sonner';
import { API_ROUTES } from '@/constants/api.routes';
import axiosInstance from '@/lib/axios';
import {
  IS3UploadVariables,
  IPresignedUrlResponse,
  IS3UploadResponse
} from '@/types/mutation.types';
import { ApiError } from '@/types/common.types';

const uploadS3Image = async ({
  file,
  fileName,
  path
}: IS3UploadVariables): Promise<IS3UploadResponse> => {
  try {
    // Step 1: Get presigned URL
    const presigned = await axiosInstance.get<IPresignedUrlResponse>(
      `${API_ROUTES.UPLOAD.PRESIGNED_URL}?fileName=${fileName}&fileSize=${file.size}&mimeType=${file.type}&path=${path}`
    );

    const { key, presignedUrl } = presigned.data.data;

    // Debug: Log the presigned URL details
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.log('Presigned URL received:', {
        key,
        presignedUrl: presignedUrl.substring(0, 100) + '...'
      });
    }

    // Step 2: Upload file to S3 using fetch with proper error handling
    const uploadRes = await fetch(presignedUrl, {
      method: 'PUT',
      body: file,
      headers: {
        'Content-Type': file.type
        // Remove any additional headers that might cause CORS issues
      }
    });

    if (uploadRes.ok) {
      const finalUrl = `${process.env.NEXT_PUBLIC_AWS_BUCKET_URL}/${key}`;
      if (process.env.NODE_ENV === 'development') {
        // eslint-disable-next-line no-console
        console.log('Upload successful, final URL:', finalUrl);
      }
      return { url: finalUrl };
    }

    // Get response text for better error debugging
    const errorText = await uploadRes.text();
    throw new Error(
      `Upload to S3 failed: ${uploadRes.status} ${uploadRes.statusText}. Response: ${errorText}`
    );
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.error('S3 Upload Error Details:', error);
    }
    throw error;
  }
};

export const useS3ImageUpload = <TError = ApiError, TContext = unknown>(
  options?: UseMutationOptions<
    IS3UploadResponse,
    TError,
    IS3UploadVariables,
    TContext
  >
) => {
  const { mutateAsync, isPending, ...props } = useMutation<
    IS3UploadResponse,
    TError,
    IS3UploadVariables,
    TContext
  >({
    mutationKey: ['s3-image-upload'],
    mutationFn: uploadS3Image,
    onSuccess: () => {
      toast.success('Image uploaded successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to upload image');
    },
    ...options
  });

  return { upload: mutateAsync, isUploading: isPending, ...props };
};
