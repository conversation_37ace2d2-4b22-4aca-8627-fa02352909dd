'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { ArrowLeft, Plus, Trash2 } from 'lucide-react';

import PageContainer from '@/components/layout/page-container';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { ImageUploadField } from '@/components/cms/image-upload-field';

import {
  useGetOpportunitySection,
  useGetTalentSection,
  useGetDiscoverSection,
  useGetCompaniesSection,
  useGetPartnersSection,
  useGetCtaSection,
  useGetAppSection
} from '@/hooks/useQuery';

import {
  useUpdateOpportunitySection,
  useUpdateTalentSection,
  useUpdateDiscoverSection,
  useUpdateCompaniesSection,
  useUpdatePartnersSection,
  useUpdateCtaSection,
  useUpdateAppSection
} from '@/hooks/useMutation';
import { DEFAULT_IMAGE } from '@/constants/app.const';

// Types are now defined locally

// import type {
//   IUpdateOpportunitySection,
//   IUpdateTalentSection,
//   IUpdateDiscoverSection,
//   IUpdateCompaniesSection,
//   IUpdatePartnersSection,
//   IUpdateCtaSection,
//   IUpdateAppSection
// } from '@/types/mutation.types';

// Types
export type SectionType =
  | 'opportunity'
  | 'talent'
  | 'discover'
  | 'companies'
  | 'partners'
  | 'cta'
  | 'app';

// type SectionData =
//   | IOpportunitySection
//   | ITalentSection
//   | IDiscoverSection
//   | ICompaniesSection
//   | IPartnersSection
//   | ICtaSection
//   | IAppSection;

type StepData = {
  heading: string;
  description: string;
};

type PartnerData = {
  name: string;
  imageURL: string;
};

interface FormData {
  heading: string;
  description: string;
  subheading?: string;
  image?: string;
  appStoreURL?: string;
  playStoreURL?: string;
  steps: StepData[];
  partners: PartnerData[];
}

// Custom hook to handle section data fetching
function useSectionData(section: SectionType) {
  const opportunityQuery = useGetOpportunitySection();
  const talentQuery = useGetTalentSection();
  const discoverQuery = useGetDiscoverSection();
  const companiesQuery = useGetCompaniesSection();
  const partnersQuery = useGetPartnersSection();
  const ctaQuery = useGetCtaSection();
  const appQuery = useGetAppSection();

  const queryMap = useMemo(
    () => ({
      opportunity: opportunityQuery,
      talent: talentQuery,
      discover: discoverQuery,
      companies: companiesQuery,
      partners: partnersQuery,
      cta: ctaQuery,
      app: appQuery
    }),
    [
      opportunityQuery,
      talentQuery,
      discoverQuery,
      companiesQuery,
      partnersQuery,
      ctaQuery,
      appQuery
    ]
  );

  return queryMap[section] || { data: null, isLoading: false };
}

// Custom hook to handle section mutations
function useSectionMutation(section: SectionType, onSuccess: () => void) {
  const opportunityMutation = useUpdateOpportunitySection({ onSuccess });
  const talentMutation = useUpdateTalentSection({ onSuccess });
  const discoverMutation = useUpdateDiscoverSection({ onSuccess });
  const companiesMutation = useUpdateCompaniesSection({ onSuccess });
  const partnersMutation = useUpdatePartnersSection({ onSuccess });
  const ctaMutation = useUpdateCtaSection({ onSuccess });
  const appMutation = useUpdateAppSection({ onSuccess });

  const mutationMap = useMemo(
    () => ({
      opportunity: opportunityMutation,
      talent: talentMutation,
      discover: discoverMutation,
      companies: companiesMutation,
      partners: partnersMutation,
      cta: ctaMutation,
      app: appMutation
    }),
    [
      opportunityMutation,
      talentMutation,
      discoverMutation,
      companiesMutation,
      partnersMutation,
      ctaMutation,
      appMutation
    ]
  );

  return mutationMap[section] || { mutate: () => {}, isPending: false };
}

// Section configuration
const SECTION_CONFIG: Record<
  SectionType,
  { title: string; description: string }
> = {
  opportunity: {
    title: 'Opportunity Section',
    description: 'Update the content of this section'
  },
  talent: {
    title: 'Talent Section',
    description: 'Update the content of this section'
  },
  discover: {
    title: 'Discover Section',
    description: 'Update the content of this section'
  },
  companies: {
    title: 'Companies Section',
    description: 'Update the content of this section'
  },
  partners: {
    title: 'Partners Section',
    description: 'Update the content of this section'
  },
  cta: {
    title: 'Call to Action Section',
    description: 'Update the content of this section'
  },
  app: {
    title: 'Mobile App Section',
    description: 'Update the content of this section'
  }
} as const;

export default function EditSectionPage() {
  const params = useParams();
  const router = useRouter();
  const section = params.section as SectionType;

  const [formData, setFormData] = useState<FormData>({
    heading: '',
    description: '',
    image: '',
    appStoreURL: '',
    playStoreURL: '',
    steps: [],
    partners: []
  });

  const onSuccess = useCallback(() => {
    router.push('/dashboard/cms/home');
  }, [router]);

  const { data, isLoading } = useSectionData(section);
  const { mutate: updateSection, isPending } = useSectionMutation(
    section,
    onSuccess
  );
  const sectionData = data?.data?.section;
  const config = SECTION_CONFIG[section];

  useEffect(() => {
    if (sectionData) {
      setFormData({
        heading: sectionData.heading || '',
        description: sectionData.description || '',
        subheading: (sectionData as any).subheading || '',
        image: (sectionData as any).image || '',
        appStoreURL: (sectionData as any).appStoreURL || '',
        playStoreURL: (sectionData as any).playStoreURL || '',
        steps:
          (sectionData as any).steps?.map((step: any) => ({
            heading: step.heading,
            description: step.description
          })) || [],
        partners:
          (sectionData as any).partners?.map((partner: any) => ({
            name: partner.name,
            imageURL: partner.imageURL
          })) || []
      });
    }
  }, [sectionData]);

  const handleBack = useCallback(() => {
    router.push('/dashboard/cms/home');
  }, [router]);

  const handleInputChange = useCallback(
    (field: keyof FormData, value: string) => {
      setFormData((prev) => ({
        ...prev,
        [field]: value
      }));
    },
    []
  );

  const handleStepChange = useCallback(
    (index: number, field: keyof StepData, value: string) => {
      setFormData((prev) => ({
        ...prev,
        steps: prev.steps.map((step, i) =>
          i === index ? { ...step, [field]: value } : step
        )
      }));
    },
    []
  );

  const addStep = useCallback(() => {
    setFormData((prev) => ({
      ...prev,
      steps: [...prev.steps, { heading: '', description: '' }]
    }));
  }, []);

  const removeStep = useCallback((index: number) => {
    setFormData((prev) => ({
      ...prev,
      steps: prev.steps.filter((_, i) => i !== index)
    }));
  }, []);

  const handlePartnerChange = useCallback(
    (index: number, field: keyof PartnerData, value: string) => {
      setFormData((prev) => ({
        ...prev,
        partners: prev.partners.map((partner, i) =>
          i === index ? { ...partner, [field]: value } : partner
        )
      }));
    },
    []
  );

  const addPartner = useCallback(() => {
    setFormData((prev) => ({
      ...prev,
      partners: [...prev.partners, { name: '', imageURL: '' }]
    }));
  }, []);

  const removePartner = useCallback((index: number) => {
    setFormData((prev) => ({
      ...prev,
      partners: prev.partners.filter((_, i) => i !== index)
    }));
  }, []);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    if (!formData.heading.trim() || !formData.description.trim()) {
      return;
    }

    // Prepare data based on section type
    const submitData: any = {
      heading: formData.heading.trim(),
      description: formData.description.trim()
    };

    // Add subheading for discover and companies sections
    if (
      (section === 'discover' || section === 'companies') &&
      formData.subheading?.trim()
    ) {
      submitData.subheading = formData.subheading.trim();
    }

    // Add image for opportunity, talent, and cta sections
    if (
      (section === 'opportunity' ||
        section === 'talent' ||
        section === 'cta') &&
      formData.image?.trim()
    ) {
      submitData.image = formData.image.trim();
    }

    // Add section-specific fields
    if (section === 'app') {
      if (formData.appStoreURL?.trim()) {
        submitData.appStoreURL = formData.appStoreURL.trim();
      }
      if (formData.playStoreURL?.trim()) {
        submitData.playStoreURL = formData.playStoreURL.trim();
      }
    }

    // Add steps if they exist
    if (formData.steps && formData.steps.length > 0) {
      const validSteps = formData.steps.filter(
        (step: any) => step.heading.trim() && step.description.trim()
      );
      if (validSteps.length > 0) {
        submitData.steps = validSteps;
      }
    }

    // Add partners if they exist
    if (formData.partners && formData.partners.length > 0) {
      const validPartners = formData.partners
        .filter(
          (partner: any) => partner.name.trim() && partner.imageURL.trim()
        )
        .map((partner: any) => ({
          name: partner.name.trim(),
          imageURL: partner.imageURL.trim()
        }));
      if (validPartners.length > 0) {
        submitData.partners = validPartners;
      }
    }

    updateSection(submitData);
  };

  if (isLoading) {
    return (
      <PageContainer>
        <div className='flex flex-1 flex-col space-y-6'>
          <div className='flex items-center gap-4'>
            <Skeleton className='h-10 w-10' />
            <div className='space-y-2'>
              <Skeleton className='h-8 w-64' />
              <Skeleton className='h-4 w-48' />
            </div>
          </div>
          <div className='space-y-4'>
            <Skeleton className='h-32 w-full' />
            <Skeleton className='h-32 w-full' />
            <Skeleton className='h-32 w-full' />
          </div>
        </div>
      </PageContainer>
    );
  }

  if (!sectionData) {
    return (
      <PageContainer>
        <div className='flex flex-1 flex-col space-y-6'>
          <div className='flex items-center gap-4'>
            <Button variant='ghost' size='sm' onClick={handleBack}>
              <ArrowLeft className='mr-2 h-4 w-4' />
              Back to CMS Home
            </Button>
          </div>
          <div className='py-12 text-center'>
            <h2 className='mb-2 text-2xl font-bold'>No Data Available</h2>
            <p className='text-muted-foreground'>
              This section doesn&apos;t have any data yet.
            </p>
          </div>
        </div>
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <div className='flex flex-1 flex-col space-y-6'>
        {/* Header */}
        <div className='flex items-center gap-4'>
          <Button variant='ghost' size='sm' onClick={handleBack}>
            <ArrowLeft className='mr-2 h-4 w-4' />
            Back to CMS Home
          </Button>
          <div>
            <h1 className='text-3xl font-bold'>Edit {config.title}</h1>
            <p className='text-muted-foreground'>{config.description}</p>
          </div>
        </div>

        {/* Form */}
        <Card>
          <CardHeader>
            <CardTitle>Section Content</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className='space-y-6'>
              <div className='space-y-6'>
                {/* Basic Fields */}
                <div className='space-y-4'>
                  <div className='space-y-3'>
                    <Label htmlFor='heading'>Heading *</Label>
                    <Input
                      id='heading'
                      value={formData.heading}
                      onChange={(e) =>
                        handleInputChange('heading', e.target.value)
                      }
                      placeholder='Enter section heading'
                      required
                    />
                  </div>

                  <div className='space-y-3'>
                    <Label htmlFor='description'>Description *</Label>
                    <Textarea
                      id='description'
                      value={formData.description}
                      onChange={(e) =>
                        handleInputChange('description', e.target.value)
                      }
                      placeholder='Enter section description'
                      rows={3}
                      required
                    />
                  </div>

                  {/* Subheading for discover and companies sections */}
                  {(section === 'discover' || section === 'companies') && (
                    <div className='space-y-3'>
                      <Label htmlFor='subheading'>Subheading</Label>
                      <Input
                        id='subheading'
                        value={formData.subheading || ''}
                        onChange={(e) =>
                          handleInputChange('subheading', e.target.value)
                        }
                        placeholder='Enter section subheading'
                      />
                    </div>
                  )}

                  {/* Image for opportunity, talent, and cta sections */}
                  {(section === 'opportunity' ||
                    section === 'talent' ||
                    section === 'cta') && (
                    <ImageUploadField
                      label='Section Image'
                      value={formData.image || DEFAULT_IMAGE}
                      onChange={(url) => handleInputChange('image', url)}
                      uploadPath={`/uploads/cms/${section}`}
                      disabled={isPending}
                    />
                  )}

                  {/* App Store URLs for app section */}
                  {section === 'app' && (
                    <>
                      <div className='space-y-3'>
                        <Label htmlFor='appStoreURL'>App Store URL</Label>
                        <Input
                          id='appStoreURL'
                          value={formData.appStoreURL}
                          onChange={(e) =>
                            handleInputChange('appStoreURL', e.target.value)
                          }
                          placeholder='Enter App Store URL'
                          type='url'
                        />
                      </div>
                      <div className='space-y-3'>
                        <Label htmlFor='playStoreURL'>Play Store URL</Label>
                        <Input
                          id='playStoreURL'
                          value={formData.playStoreURL}
                          onChange={(e) =>
                            handleInputChange('playStoreURL', e.target.value)
                          }
                          placeholder='Enter Play Store URL'
                          type='url'
                        />
                      </div>
                    </>
                  )}
                </div>

                {/* Steps Section - for sections that have steps */}
                {(section === 'opportunity' || section === 'talent') && (
                  <div>
                    <div className='mb-4 flex items-center justify-between'>
                      <Label className='text-base font-medium'>Steps</Label>
                      <Button
                        type='button'
                        variant='outline'
                        size='sm'
                        onClick={addStep}
                      >
                        <Plus className='mr-1 h-4 w-4' />
                        Add Step
                      </Button>
                    </div>

                    <div className='space-y-4'>
                      {formData.steps.map((step: any, index: number) => (
                        <Card
                          key={index}
                          className='border-l-primary border-l-4'
                        >
                          <CardHeader className='pb-3'>
                            <div className='flex items-center justify-between'>
                              <CardTitle className='text-sm'>
                                Step {index + 1}
                              </CardTitle>
                              <Button
                                type='button'
                                variant='ghost'
                                size='sm'
                                onClick={() => removeStep(index)}
                                className='text-destructive hover:text-destructive'
                              >
                                <Trash2 className='h-4 w-4' />
                              </Button>
                            </div>
                          </CardHeader>
                          <CardContent className='space-y-3'>
                            <div>
                              <Label htmlFor={`step-heading-${index}`}>
                                Step Heading
                              </Label>
                              <Input
                                id={`step-heading-${index}`}
                                value={step.heading}
                                onChange={(e) =>
                                  handleStepChange(
                                    index,
                                    'heading',
                                    e.target.value
                                  )
                                }
                                placeholder='Enter step heading'
                              />
                            </div>
                            <div>
                              <Label htmlFor={`step-description-${index}`}>
                                Step Description
                              </Label>
                              <Textarea
                                id={`step-description-${index}`}
                                value={step.description}
                                onChange={(e) =>
                                  handleStepChange(
                                    index,
                                    'description',
                                    e.target.value
                                  )
                                }
                                placeholder='Enter step description'
                                rows={2}
                              />
                            </div>
                          </CardContent>
                        </Card>
                      ))}

                      {formData.steps.length === 0 && (
                        <div className='text-muted-foreground py-8 text-center'>
                          <p>
                            No steps added yet. Click &quot;Add Step&quot; to
                            create your first step.
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Partners Section - for sections that have partners */}
                {section === 'partners' && (
                  <div>
                    <div className='mb-4 flex items-center justify-between'>
                      <Label className='text-base font-medium'>Partners</Label>
                      <Button
                        type='button'
                        variant='outline'
                        size='sm'
                        onClick={addPartner}
                      >
                        <Plus className='mr-1 h-4 w-4' />
                        Add Partner
                      </Button>
                    </div>

                    <div className='space-y-4'>
                      {formData.partners.map((partner: any, index: number) => (
                        <Card
                          key={index}
                          className='border-l-primary border-l-4'
                        >
                          <CardHeader className='pb-3'>
                            <div className='flex items-center justify-between'>
                              <CardTitle className='text-sm'>
                                Partner {index + 1}
                              </CardTitle>
                              <Button
                                type='button'
                                variant='ghost'
                                size='sm'
                                onClick={() => removePartner(index)}
                                className='text-destructive hover:text-destructive'
                              >
                                <Trash2 className='h-4 w-4' />
                              </Button>
                            </div>
                          </CardHeader>
                          <CardContent className='space-y-3'>
                            <div>
                              <Label htmlFor={`partner-name-${index}`}>
                                Partner Name
                              </Label>
                              <Input
                                id={`partner-name-${index}`}
                                value={partner.name}
                                onChange={(e) =>
                                  handlePartnerChange(
                                    index,
                                    'name',
                                    e.target.value
                                  )
                                }
                                placeholder='Enter partner name'
                              />
                            </div>
                            <ImageUploadField
                              label='Partner Image'
                              value={partner.imageURL || DEFAULT_IMAGE}
                              onChange={(url) =>
                                handlePartnerChange(index, 'imageURL', url)
                              }
                              uploadPath={`/uploads/cms/partners`}
                              disabled={isPending}
                            />
                          </CardContent>
                        </Card>
                      ))}

                      {formData.partners.length === 0 && (
                        <div className='text-muted-foreground py-8 text-center'>
                          <p>
                            No partners added yet. Click &quot;Add Partner&quot;
                            to create your first partner.
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>

              {/* Form Actions */}
              <div className='flex gap-4 border-t pt-4'>
                <Button
                  type='button'
                  variant='outline'
                  onClick={handleBack}
                  disabled={isPending}
                >
                  Cancel
                </Button>
                <Button
                  type='submit'
                  disabled={
                    isPending ||
                    !formData.heading.trim() ||
                    !formData.description.trim()
                  }
                >
                  {isPending ? 'Saving...' : 'Save Changes'}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </PageContainer>
  );
}
