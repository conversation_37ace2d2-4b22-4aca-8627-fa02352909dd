'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { FileUploader } from '@/components/file-uploader';
import { useS3ImageUpload } from '@/hooks/useS3Upload';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, XCircle, Upload } from 'lucide-react';

export function UploadTest() {
  const [files, setFiles] = useState<File[]>([]);
  const [uploadResult, setUploadResult] = useState<{
    success: boolean;
    url?: string;
    error?: string;
  } | null>(null);

  const { upload, isUploading } = useS3ImageUpload({
    onSuccess: (data) => {
      setUploadResult({ success: true, url: data.url });
      setFiles([]);
    },
    onError: (error: any) => {
      setUploadResult({ 
        success: false, 
        error: error?.response?.data?.message || error?.message || 'Upload failed' 
      });
    }
  });

  const handleTestUpload = async () => {
    if (files.length === 0) return;
    
    const file = files[0];
    const fileName = `test-${Date.now()}-${file.name}`;
    
    setUploadResult(null);
    
    try {
      await upload({
        file,
        fileName,
        path: '/uploads/test'
      });
    } catch (error) {
      // Error handled by onError callback
    }
  };

  const resetTest = () => {
    setFiles([]);
    setUploadResult(null);
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Upload className="h-5 w-5" />
          S3 Upload Test
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* File Uploader */}
        <div>
          <h3 className="text-sm font-medium mb-3">Select Test File</h3>
          <FileUploader
            value={files}
            onValueChange={setFiles}
            maxFiles={1}
            maxSize={5 * 1024 * 1024} // 5MB
            accept={{ 'image/*': ['.png', '.jpg', '.jpeg', '.webp'] }}
            disabled={isUploading}
          />
        </div>

        {/* Upload Button */}
        <div className="flex gap-2">
          <Button 
            onClick={handleTestUpload}
            disabled={files.length === 0 || isUploading}
            className="flex-1"
          >
            {isUploading ? 'Uploading...' : 'Test Upload'}
          </Button>
          <Button 
            variant="outline" 
            onClick={resetTest}
            disabled={isUploading}
          >
            Reset
          </Button>
        </div>

        {/* Upload Status */}
        {isUploading && (
          <Alert>
            <Upload className="h-4 w-4 animate-spin" />
            <AlertDescription>
              Uploading file to S3... Please wait.
            </AlertDescription>
          </Alert>
        )}

        {/* Upload Result */}
        {uploadResult && (
          <Alert className={uploadResult.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
            {uploadResult.success ? (
              <CheckCircle className="h-4 w-4 text-green-600" />
            ) : (
              <XCircle className="h-4 w-4 text-red-600" />
            )}
            <AlertDescription>
              {uploadResult.success ? (
                <div className="space-y-2">
                  <p className="text-green-800 font-medium">Upload Successful!</p>
                  <p className="text-sm text-green-700">
                    URL: <code className="bg-green-100 px-1 rounded text-xs">{uploadResult.url}</code>
                  </p>
                </div>
              ) : (
                <div className="space-y-2">
                  <p className="text-red-800 font-medium">Upload Failed</p>
                  <p className="text-sm text-red-700">{uploadResult.error}</p>
                </div>
              )}
            </AlertDescription>
          </Alert>
        )}

        {/* Debug Info */}
        <div className="text-xs text-gray-500 space-y-1">
          <p><Badge variant="outline">Environment</Badge> {process.env.NODE_ENV}</p>
          <p><Badge variant="outline">Bucket URL</Badge> {process.env.NEXT_PUBLIC_AWS_BUCKET_URL || 'Not set'}</p>
          <p><Badge variant="outline">API URL</Badge> {process.env.NEXT_PUBLIC_API_URL || 'Not set'}</p>
        </div>
      </CardContent>
    </Card>
  );
}
