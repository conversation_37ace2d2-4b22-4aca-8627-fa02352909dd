'use client';

import { useRouter } from 'next/navigation';
import PageContainer from '@/components/layout/page-container';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Eye, Edit } from 'lucide-react';
import { useGetHowItWorksPage } from '@/hooks/useQuery';

export default function HowItWorksCMSPage() {
  const router = useRouter();

  // Fetch How It Works page data
  const { data: howItWorksData, isLoading: howItWorksLoading } = useGetHowItWorksPage();

  const sections = [
    {
      id: 'main-section',
      title: 'Main Section',
      description: 'Main heading, description and hero image',
      data: howItWorksData?.data?.page ? {
        heading: howItWorksData.data.page.heading,
        description: howItWorksData.data.page.description,
        image: howItWorksData.data.page.image
      } : null,
      isLoading: howItWorksLoading,
      color: 'bg-blue-50 border-blue-200',
      badge: 'primary'
    },
    {
      id: 'for-candidates',
      title: 'For Candidates',
      description: 'How it works section for job seekers',
      data: howItWorksData?.data?.page?.forCandidates,
      isLoading: howItWorksLoading,
      color: 'bg-green-50 border-green-200',
      badge: 'secondary'
    },
    {
      id: 'for-recruiters',
      title: 'For Recruiters',
      description: 'How it works section for recruiters',
      data: howItWorksData?.data?.page?.forRecruiters,
      isLoading: howItWorksLoading,
      color: 'bg-purple-50 border-purple-200',
      badge: 'outline'
    },
    {
      id: 'benefits',
      title: 'Benefits',
      description: 'Platform benefits and features',
      data: howItWorksData?.data?.page?.benefits,
      isLoading: howItWorksLoading,
      color: 'bg-orange-50 border-orange-200',
      badge: 'secondary'
    }
  ];

  const handleView = () => {
    router.push('/dashboard/cms/how-it-works/view');
  };

  const handleEdit = () => {
    router.push('/dashboard/cms/how-it-works/edit');
  };

  const renderSectionCard = (section: any) => {
    if (section.isLoading) {
      return (
        <Card key={section.id} className='h-full'>
          <CardHeader>
            <Skeleton className='h-6 w-3/4' />
            <Skeleton className='h-4 w-full' />
          </CardHeader>
          <CardContent>
            <div className='space-y-2'>
              <Skeleton className='h-4 w-full' />
              <Skeleton className='h-4 w-2/3' />
            </div>
          </CardContent>
        </Card>
      );
    }

    return (
      <Card
        key={section.id}
        className={`h-full transition-all hover:shadow-md ${section.color}`}
      >
        <CardHeader>
          <div className='flex items-center justify-between'>
            <CardTitle className='text-lg'>{section.title}</CardTitle>
            <Badge variant={section.badge as any}>{section.id}</Badge>
          </div>
          <p className='text-muted-foreground text-sm'>{section.description}</p>
        </CardHeader>
        <CardContent>
          <div className='space-y-3'>
            {section.data ? (
              <div className='space-y-2'>
                <div>
                  <span className='text-sm font-medium'>Heading:</span>
                  <p className='text-muted-foreground truncate text-sm'>
                    {section.data.heading}
                  </p>
                </div>
                <div>
                  <span className='text-sm font-medium'>Description:</span>
                  <p className='text-muted-foreground line-clamp-2 text-sm'>
                    {section.data.description}
                  </p>
                </div>
                {section.data.steps && (
                  <div>
                    <span className='text-sm font-medium'>Steps:</span>
                    <p className='text-muted-foreground text-sm'>
                      {section.data.steps.length} steps
                    </p>
                  </div>
                )}
                {Array.isArray(section.data) && (
                  <div>
                    <span className='text-sm font-medium'>Benefits:</span>
                    <p className='text-muted-foreground text-sm'>
                      {section.data.length} benefits
                    </p>
                  </div>
                )}
                {section.data.image && (
                  <div>
                    <span className='text-sm font-medium'>Image:</span>
                    <p className='text-muted-foreground text-sm'>Available</p>
                  </div>
                )}
              </div>
            ) : (
              <p className='text-muted-foreground text-sm'>No data available</p>
            )}
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <PageContainer>
      <div className='flex flex-1 flex-col space-y-6'>
        <div>
          <h1 className='text-3xl font-bold'>How It Works Page</h1>
          <p className='text-muted-foreground'>
            Manage the How It Works page content. View and edit all sections to
            customize the user journey explanation.
          </p>
        </div>

        <div className='grid grid-cols-1 gap-6 md:grid-cols-2'>
          {sections.map(renderSectionCard)}
        </div>

        <div className='flex gap-4 pt-6'>
          <Button
            variant='outline'
            size='lg'
            onClick={handleView}
            disabled={howItWorksLoading || !howItWorksData?.data?.page}
          >
            <Eye className='mr-2 h-4 w-4' />
            View All Sections
          </Button>
          <Button
            size='lg'
            onClick={handleEdit}
            disabled={howItWorksLoading || !howItWorksData?.data?.page}
          >
            <Edit className='mr-2 h-4 w-4' />
            Edit All Sections
          </Button>
        </div>
      </div>
    </PageContainer>
  );
}
